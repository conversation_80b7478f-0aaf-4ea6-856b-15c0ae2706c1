# 移动端钱币图片展示优化

## 优化内容

### 1. 图片展示改进
- **响应式高度**：从固定300px改为动态高度（280px-50vh），适应不同屏幕尺寸
- **更好的图片适配**：保持`fit="contain"`确保图片完整显示
- **加载状态**：添加图片加载指示器
- **错误处理**：优化图片加载失败的处理

### 2. 交互体验优化
- **触摸滑动**：支持左右滑动切换图片
- **左右切换按钮**：添加悬浮的切换按钮
- **指示器点**：添加图片指示器，直观显示当前位置
- **点击预览提示**：添加"点击查看大图"提示

### 3. 视觉效果提升
- **圆角设计**：使用12px圆角，更现代的视觉效果
- **阴影效果**：添加适度的阴影，增强层次感
- **动画过渡**：添加平滑的过渡动画
- **毛玻璃效果**：标签和提示使用backdrop-filter

### 4. 响应式适配
- **小屏幕适配**：480px以下屏幕的特殊优化
- **中等屏幕适配**：481px-768px屏幕的适配
- **横屏适配**：横屏模式下的高度调整
- **高分辨率屏幕**：Retina屏幕的优化
- **触摸设备**：触摸设备的按钮尺寸优化

## 主要功能

### 触摸滑动
- 向左滑动：显示下一张图片
- 向右滑动：显示上一张图片
- 最小滑动距离：50px
- 防误触：区分水平和垂直滑动

### 图片预览
- 点击图片：打开Element Plus的图片预览
- 支持缩放、旋转等操作
- 支持多图片浏览

### 切换方式
1. 触摸滑动
2. 左右切换按钮
3. 指示器点击
4. 底部按钮点击

## 技术实现

### 新增响应式数据
```javascript
const imageLoading = ref(false);
const touchStartX = ref(0);
const touchStartY = ref(0);
const touchEndX = ref(0);
const touchEndY = ref(0);
```

### 新增方法
- `prevImage()`: 切换到上一张图片
- `nextImage()`: 切换到下一张图片
- `openImagePreview()`: 打开图片预览
- `onImageLoad()`: 图片加载完成处理
- `onImageError()`: 图片加载错误处理
- `onTouchStart()`: 触摸开始处理
- `onTouchMove()`: 触摸移动处理
- `onTouchEnd()`: 触摸结束处理

### CSS优化
- 使用CSS Grid和Flexbox布局
- 响应式媒体查询
- 硬件加速的动画
- 现代的视觉效果

## 兼容性
- iOS Safari 12+
- Android Chrome 70+
- 微信内置浏览器
- 支持触摸和鼠标操作

## 使用说明
1. 在移动端访问钱币详情页面
2. 可以通过多种方式切换图片：
   - 左右滑动
   - 点击左右切换按钮
   - 点击指示器点
   - 点击底部切换按钮
3. 点击图片可以查看大图
4. 支持图片的缩放和旋转操作
