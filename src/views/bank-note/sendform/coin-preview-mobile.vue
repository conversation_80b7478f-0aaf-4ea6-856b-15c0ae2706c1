<template>
  <div class="coin-preview-mobile-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{ coinData?.display?.coinName || '钱币详情' }}</h1>
        <div class="header-code" v-if="coinData?.coin?.diyCode">
          编码：{{ coinData.coin.diyCode }}
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="4" animated />
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-else-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <!-- 钱币详情主体 -->
    <div v-else-if="coinData" class="coin-details-main">
      <!-- 钱币图片区域 - 移动端优化 -->
      <div class="image-section">
        <div v-if="coinImages.length > 0" class="image-container">
          <!-- 图片轮播容器 -->
          <div class="image-swiper-container">
            <!-- 当前显示的图片 -->
            <div
              class="current-image"
              @click="openImagePreview"
              @touchstart="onTouchStart"
              @touchmove="onTouchMove"
              @touchend="onTouchEnd"
            >
              <div class="image-label">{{ getImageLabel(currentImageIndex) }}</div>
              <div class="image-wrapper">
                <el-image
                  :src="coinImages[currentImageIndex]"
                  :preview-src-list="coinImages"
                  :initial-index="currentImageIndex"
                  fit="contain"
                  class="main-image"
                  :preview-teleported="true"
                  loading="lazy"
                  @load="onImageLoad"
                  @error="onImageError"
                />
                <!-- 图片加载状态 -->
                <div v-if="imageLoading" class="image-loading">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <span>加载中...</span>
                </div>
              </div>
              <!-- 点击放大提示 -->
              <div class="zoom-hint">
                <el-icon><ZoomIn /></el-icon>
                <span>点击查看大图</span>
              </div>
            </div>

            <!-- 左右切换按钮 -->
            <div class="swipe-controls" v-if="coinImages.length > 1">
              <el-button
                class="swipe-btn swipe-prev"
                :disabled="currentImageIndex === 0"
                @click="prevImage"
                circle
                size="small"
              >
                <el-icon><ArrowLeft /></el-icon>
              </el-button>
              <el-button
                class="swipe-btn swipe-next"
                :disabled="currentImageIndex === coinImages.length - 1"
                @click="nextImage"
                circle
                size="small"
              >
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 图片指示器和切换按钮 -->
          <div class="image-controls" v-if="coinImages.length > 1">
            <!-- 指示器点 -->
            <div class="image-indicators">
              <span
                v-for="(image, index) in coinImages"
                :key="index"
                :class="['indicator-dot', { active: currentImageIndex === index }]"
                @click="currentImageIndex = index"
              ></span>
            </div>
            <!-- 切换按钮 -->
            <div class="image-buttons">
              <el-button
                v-for="(image, index) in coinImages"
                :key="index"
                :type="currentImageIndex === index ? 'primary' : 'default'"
                size="small"
                @click="currentImageIndex = index"
                class="image-btn"
              >
                {{ getImageLabel(index) }}
              </el-button>
            </div>
          </div>
        </div>
        <div v-else class="no-image">
          <el-icon class="no-image-icon"><Picture /></el-icon>
          <p>暂无图片</p>
        </div>
      </div>

      <!-- 基本信息区域 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-list">
          <div class="info-item">
            <span class="info-label">钱币名称</span>
            <span class="info-value">{{ coinData?.display?.coinName || '未设置' }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">版别</span>
            <span class="info-value">{{ coinData?.display?.serialNumberWithVersion || '未设置' }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">编码</span>
            <span class="info-value code">{{ coinData?.coin?.diyCode || '未设置' }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">发行银行</span>
            <span class="info-value">{{ coinData?.coin?.issueBank || '中国人民银行' }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">冠号</span>
            <span class="info-value">{{ coinData?.coin?.serialNumber || '未设置' }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">年代</span>
            <span class="info-value">{{ coinData?.coin?.yearInfo || '未设置' }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">评级结果</span>
            <span class="info-value grade">{{ formatGradeScore(coinData?.coin?.gradeScore, coinData?.coin?.gradeScoreValue) }}</span>
          </div>

          <div class="info-item" v-if="coinData?.coin?.compensationLevel">
            <span class="info-label">赔付等级</span>
            <span class="info-value">{{ coinData.coin.compensationLevel }}</span>
          </div>

          <div class="info-item full-width" v-if="coinData?.coin?.remark">
            <span class="info-label">备注</span>
            <span class="info-value">{{ coinData.coin.remark }}</span>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="action-bar">
        <el-button @click="goBack" class="action-btn" size="large">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button type="primary" @click="refresh" class="action-btn" size="large">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import axios from 'axios';
  import { API_BASE_URL } from '@/config/setting';
  import {
    Picture,
    ArrowLeft,
    ArrowRight,
    Refresh,
    ZoomIn,
    Loading
  } from '@element-plus/icons-vue';

  const route = useRoute();
  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const error = ref('');
  const coinData = ref(null);
  const currentImageIndex = ref(0);
  const imageLoading = ref(false);

  // 触摸滑动相关
  const touchStartX = ref(0);
  const touchStartY = ref(0);
  const touchEndX = ref(0);
  const touchEndY = ref(0);

  // 计算属性
  const coinImages = computed(() => {
    const images = [];
    const coin = coinData.value?.coin;

    if (coin?.frontImage) {
      images.push(coin.frontImage);
    }
    if (coin?.backImage) {
      images.push(coin.backImage);
    }

    return images;
  });

  // 方法
  const loadCoinData = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      error.value = '缺少送评条码参数';
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      const res = await axios.get(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`
      );
      if (res.data.code === 0) {
        coinData.value = res.data.data;
      } else {
        throw new Error(res.data.message);
      }
    } catch (err) {
      error.value = err.message || '加载钱币详情失败';
      ElMessage.error(error.value);
    } finally {
      loading.value = false;
    }
  };

  const formatGradeScore = (gradeScore, gradeScoreValue) => {
    if (!gradeScore && !gradeScoreValue) {
      return '未评级';
    }

    if (gradeScoreValue && gradeScore) {
      const gradeText = gradeScore.replace(gradeScoreValue, '').trim();
      return `${gradeText} ${gradeScoreValue}`;
    }

    if (gradeScore) {
      return gradeScore;
    }

    if (gradeScoreValue) {
      return gradeScoreValue;
    }

    return '未评级';
  };

  const getImageLabel = (index) => {
    const labels = ['钱币正面', '钱币反面'];
    return labels[index] || `图片${index + 1}`;
  };

  const goBack = () => {
    router.back();
  };

  const refresh = () => {
    loadCoinData();
  };

  // 图片相关方法
  const prevImage = () => {
    if (currentImageIndex.value > 0) {
      currentImageIndex.value--;
    }
  };

  const nextImage = () => {
    if (currentImageIndex.value < coinImages.value.length - 1) {
      currentImageIndex.value++;
    }
  };

  const openImagePreview = () => {
    // 触发el-image的预览功能
    const imageEl = document.querySelector('.main-image img');
    if (imageEl) {
      imageEl.click();
    }
  };

  const onImageLoad = () => {
    imageLoading.value = false;
  };

  const onImageError = () => {
    imageLoading.value = false;
    ElMessage.error('图片加载失败');
  };

  // 触摸滑动事件处理
  const onTouchStart = (e) => {
    touchStartX.value = e.touches[0].clientX;
    touchStartY.value = e.touches[0].clientY;
  };

  const onTouchMove = (e) => {
    // 阻止默认滚动行为，但允许图片预览
    if (Math.abs(e.touches[0].clientX - touchStartX.value) > 10) {
      e.preventDefault();
    }
  };

  const onTouchEnd = (e) => {
    touchEndX.value = e.changedTouches[0].clientX;
    touchEndY.value = e.changedTouches[0].clientY;

    const deltaX = touchEndX.value - touchStartX.value;
    const deltaY = touchEndY.value - touchStartY.value;

    // 判断是否为水平滑动（水平距离大于垂直距离且大于最小滑动距离）
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        // 向右滑动，显示上一张
        prevImage();
      } else {
        // 向左滑动，显示下一张
        nextImage();
      }
    }
  };

  // 设备检测
  const isDesktop = () => {
    const userAgent = navigator.userAgent;
    const mobileKeywords = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    const screenWidth = window.innerWidth;

    return !mobileKeywords.test(userAgent) && screenWidth > 768;
  };

  // 自动跳转到PC端页面
  const redirectToDesktop = () => {
    const currentPath = route.path;
    const desktopPath = currentPath.replace('/coin-preview-mobile/', '/coin-preview/');
    router.replace(desktopPath);
  };

  onMounted(() => {
    // 检测设备类型，PC端自动跳转
    if (isDesktop()) {
      redirectToDesktop();
      return;
    }

    loadCoinData();
  });
</script>

<style scoped>
/* 移动端专用样式 */
.coin-preview-mobile-container {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  padding: 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.header-code {
  font-size: 13px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.loading-container {
  padding: 20px;
}

.error-alert {
  margin: 16px;
}

.coin-details-main {
  padding: 16px;
  padding-bottom: 80px;
}

/* 图片区域 - 移动端优化 */
.image-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-swiper-container {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.current-image {
  position: relative;
  width: 100%;
  min-height: 280px;
  max-height: 50vh;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.current-image:active {
  transform: scale(0.98);
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-label {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  z-index: 3;
  backdrop-filter: blur(4px);
}

.main-image {
  width: 100%;
  height: 100%;
  min-height: 280px;
  max-height: 50vh;
  object-fit: contain;
  cursor: pointer;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  z-index: 2;
}

.zoom-hint {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  font-size: 11px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 3;
  backdrop-filter: blur(4px);
  opacity: 0.8;
}

/* 左右切换按钮 */
.swipe-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  pointer-events: none;
  z-index: 4;
}

.swipe-btn {
  pointer-events: all;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.swipe-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.swipe-btn:disabled {
  opacity: 0.3;
  pointer-events: none;
}

/* 图片控制区域 */
.image-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.image-indicators {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: #007bff;
  transform: scale(1.2);
}

.image-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.image-btn {
  flex: 1;
  min-width: 100px;
  max-width: 120px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.image-btn:hover {
  transform: translateY(-1px);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.no-image-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

/* 基本信息区域 */
.basic-info-section {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #007bff;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px 0;
  border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.full-width {
  gap: 8px;
}

.info-label {
  font-size: 13px;
  font-weight: 600;
  color: #6c757d;
}

.info-value {
  font-size: 15px;
  color: #212529;
  word-break: break-word;
}

.info-value.code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  display: inline-block;
}

.info-value.grade {
  font-weight: 600;
  color: #28a745;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #e9ecef;
  padding: 12px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 44px;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 480px) {
  .current-image {
    min-height: 240px;
    max-height: 40vh;
  }

  .main-image {
    min-height: 240px;
    max-height: 40vh;
  }

  .image-label {
    top: 8px;
    left: 8px;
    padding: 4px 8px;
    font-size: 11px;
  }

  .zoom-hint {
    bottom: 8px;
    right: 8px;
    padding: 4px 8px;
    font-size: 10px;
  }

  .swipe-controls {
    padding: 0 8px;
  }

  .image-btn {
    min-width: 80px;
    max-width: 100px;
    font-size: 12px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .current-image {
    min-height: 320px;
    max-height: 55vh;
  }

  .main-image {
    min-height: 320px;
    max-height: 55vh;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
  .current-image {
    min-height: 200px;
    max-height: 60vh;
  }

  .main-image {
    min-height: 200px;
    max-height: 60vh;
  }

  .coin-details-main {
    padding-bottom: 60px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .image-label,
  .zoom-hint {
    backdrop-filter: blur(8px);
  }
}

/* 触摸设备优化 */
@media (pointer: coarse) {
  .swipe-btn {
    width: 40px;
    height: 40px;
  }

  .indicator-dot {
    width: 10px;
    height: 10px;
  }

  .image-btn {
    height: 36px;
  }
}
</style>
